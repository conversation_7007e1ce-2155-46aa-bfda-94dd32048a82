buildscript {
    repositories {
        mavenLocal()
        maven {
            name = 'd2hp-v1-d2hp-v1-artifacts'
            url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
    }

    ext {
        springCloudVersion = "2025.0.0"
        sapHanaDriverVersion = '2.12.9'
    }
}

plugins {
	id 'java'
    id 'idea'
	id 'org.springframework.boot' version '3.5.4'
	id 'io.spring.dependency-management' version '1.1.7'
    id 'com.google.cloud.tools.jib' version '3.3.0'
    id 'za.co.discovery.publisher.aws-codeartifact-publisher' version '1.0.0'
    id "jacoco"
    id "org.sonarqube" version "6.1.0.5360"
}

sourceCompatibility = JavaVersion.VERSION_21
targetCompatibility = JavaVersion.VERSION_21

group = 'com.vitality.journey'
version = '0.0.1-SNAPSHOT'
description = 'Journeys data importer'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

sourceSets {
    main { java { srcDirs += file('build/generated/src/main/java') } }
    main { java { srcDirs += file('build/generated/sources/annotationProcessor/java') } }
}

idea {
    module { generatedSourceDirs += file('build/generated/src/main/java') }
    module { generatedSourceDirs += file('build/generated/sources/annotationProcessor/java') }
}

jar {
    enabled = true
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

bootJar {
    manifest {
        attributes 'Start-Class': 'com.vitality.journey.importer.JourneyImporter'
    }
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

repositories {
    mavenLocal()
    maven {
        name = 'd2hp-v1-d2hp-v1-artifacts'
        url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
        credentials {
            username "aws"
            password System.env.CODEARTIFACT_AUTH_TOKEN ?: System.getProperty("codeArtifactAuthToken")
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
}

ext {
    set('cucumberVersion', "7.20.1")
    set('junitVersion', "5.11.4")
    set('mockitoVersion', "4.2.0")
    set('flywayVersion', '11.1.0')
    set('springFox','3.0.0')
}

test {
    useJUnitPlatform()
}

tasks.withType(Checkstyle).configureEach {
    reports {
        xml.enabled false
        html.enabled true
    }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}


dependencies {
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'javax.annotation:javax.annotation-api:1.3.2'
    implementation 'io.swagger:swagger-annotations:1.6.11'

    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation('org.springframework.boot:spring-boot-starter-data-jpa') {
        exclude group: 'com.zaxxer', module: 'HikariCP'
    }

    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    implementation 'com.h2database:h2'
    implementation "org.flywaydb:flyway-core:${flywayVersion}"
    implementation 'org.apache.tomcat:tomcat-jdbc'
    implementation "com.sap.cloud.db.jdbc:ngdbc:${sapHanaDriverVersion}"

    implementation "org.flywaydb:flyway-core:${flywayVersion}"

    implementation 'org.apache.httpcomponents:httpclient:4.5.13'

    //swagger
    implementation 'za.co.discovery.health.hs:hs-starter-swagger:20240902'
    implementation "io.swagger.core.v3:swagger-annotations:2.2.15"
    implementation 'org.openapitools:jackson-databind-nullable:0.2.6'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-journey-importer:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['33772']
        jvmFlags = [
                '-XX:MinRAMPercentage=60.0',
                '-XX:MaxRAMPercentage=80.0',
                '-XX:+PrintFlagsFinal',
                '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.journey.importer.JourneyImporter'
    }
    allowInsecureRegistries = true
}